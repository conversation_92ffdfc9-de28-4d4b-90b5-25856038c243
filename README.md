# AuthenCIO CMS

A modern content management system with advanced AI-powered workflow capabilities for automated content generation, review, and publishing.

## Overview

AuthenCIO CMS combines the power of Payload CMS with sophisticated AI workflow automation, enabling content creators to generate high-quality content through intelligent agent collaboration and human-in-the-loop review processes.

## Key Features

### 🤖 AI-Powered Content Generation
- **Multi-Provider AI Support**: OpenAI, Anthropic (Claude), with <PERSON><PERSON><PERSON> (Bring Your Own Key)
- **Intelligent Agent System**: Specialized agents for SEO, market research, and content strategy
- **Dynamic Agent Consultation**: Context-aware agent collaboration during workflow execution

### 🔄 Advanced Workflow Engine
- **Template-Based Workflows**: Pre-built templates for common content generation tasks
- **Step-by-Step Execution**: Dependency management and conditional logic
- **Human Review Integration**: Approval gates and feedback collection
- **Real-time Monitoring**: Live workflow execution tracking

### 📝 Content Management
- **Payload CMS Integration**: Full-featured headless CMS capabilities
- **Artifact Management**: Version control and approval workflows for generated content
- **Multi-format Support**: Blog posts, product descriptions, social media content
- **CMS Publishing**: Direct integration with WordPress, Shopify, and other platforms

### 👥 Human-in-the-Loop
- **Review System**: Approve, reject, or provide feedback on AI-generated content
- **Collaborative Workflows**: Multi-user review and approval processes
- **Quality Assurance**: Built-in content quality evaluation and improvement

## Architecture

### Core Components

1. **Workflow Engine** (`src/core/workflow/`)
   - Template-driven workflow execution
   - Step dependency management
   - Human review integration
   - Real-time progress tracking

2. **AI Model Manager** (`src/core/ai/`)
   - Multi-provider AI integration
   - Cost tracking and usage analytics
   - BYOK functionality
   - Model selection optimization

3. **Agent System** (`src/core/agents/`)
   - Specialized AI agents (SEO, Market Research, Content Strategy)
   - Dynamic agent consultation
   - Multi-agent collaboration
   - Context-aware recommendations

4. **State Management** (`src/core/state/`)
   - Redis-backed persistent storage
   - Workflow execution tracking
   - Artifact version control
   - Event-driven updates

## Quick Start

### Prerequisites
- Node.js 18+
- Yarn package manager
- MongoDB (for CMS data)
- Redis (for workflow state)

### Installation

1. **Clone and install dependencies:**
   ```bash
   git clone <repository-url>
   cd authenciocms
   yarn install
   ```

2. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Configure your API keys and database connections
   ```

3. **Start the development server:**
   ```bash
   yarn dev
   ```

4. **Access the application:**
   - **CMS Admin**: `http://localhost:3000/admin`
   - **Workflow Interface**: `http://localhost:3000/workflow`
   - **Agent Dashboard**: `http://localhost:3000/agents/consultation`

## Workflow Templates

### Available Templates

1. **SEO Blog Post Generation**
   - Keyword research and analysis
   - Content strategy development
   - SEO-optimized content creation
   - Human review and approval

2. **Bulk Product Descriptions**
   - CSV data import
   - Market research integration
   - Batch content generation
   - Quality assurance review

3. **Content Refresh & Update**
   - Existing content analysis
   - SEO optimization recommendations
   - Content improvement suggestions
   - Version control and approval

## API Endpoints

### Workflow API
- `POST /api/workflow/create` - Create and execute workflows
- `GET /api/workflow/create?executionId=<id>` - Get execution status
- `POST /api/workflow/control` - Pause/resume/stop workflows

### Agent API
- `GET /api/agents/consultation` - Agent metrics and status
- `POST /api/agents/consultation` - Trigger agent consultations
- `POST /api/agents/selection` - Get agent recommendations

### Review API
- `GET /api/review/[id]` - Get review data
- `POST /api/review/[id]` - Submit review decisions

### CMS API
- `POST /api/cms/publish` - Publish content to CMS
- `GET /api/artifacts/[id]` - Get artifact data

## Development

### Project Structure
```
src/
├── app/                    # Next.js app router
│   ├── (payload)/         # Payload CMS admin
│   ├── workflow/          # Workflow interfaces
│   └── api/               # API routes
├── core/                  # Core business logic
│   ├── workflow/          # Workflow engine
│   ├── agents/            # AI agent system
│   ├── ai/                # AI model management
│   ├── state/             # State management
│   └── review/            # Review system
├── components/            # React components
├── collections/           # Payload CMS collections
└── utils/                 # Utility functions
```

### Key Technologies
- **Frontend**: React 18, Next.js 14, TailwindCSS
- **Backend**: Node.js, Payload CMS
- **Database**: MongoDB, Redis
- **AI**: OpenAI GPT-4, Anthropic Claude
- **State Management**: Upstash Redis
- **Testing**: Jest, React Testing Library

### Environment Variables
```bash
# Database
MONGODB_URI=mongodb://localhost:27017/authenciocms
REDIS_URL=redis://localhost:6379

# AI Providers
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key

# Payload CMS
PAYLOAD_SECRET=your_payload_secret
```

## Testing

### Running Tests
```bash
# Run all tests
yarn test

# Run tests in watch mode
yarn test:watch

# Run integration tests
yarn test:integration
```

### Test Coverage
- Unit tests for core workflow engine
- Integration tests for AI agent system
- API endpoint testing
- Component testing with React Testing Library

## Deployment

### Production Setup
1. **Environment Configuration**
   - Set production environment variables
   - Configure Redis and MongoDB connections
   - Set up AI provider API keys

2. **Build and Deploy**
   ```bash
   yarn build
   yarn start
   ```

3. **Health Checks**
   - Monitor workflow execution status
   - Check AI provider connectivity
   - Verify database connections

## Documentation

- **System Architecture**: See `docs/WHY_SIMPLIFIED_SYSTEM.md`
- **Implementation Guide**: See `README-NEW-SYSTEM.md`
- **API Documentation**: Available at `/api/docs` (when running)
- **Agent System**: See `src/core/agents/README.md`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

### Development Guidelines
- Follow TypeScript best practices
- Write comprehensive tests
- Document new features
- Maintain backward compatibility

## License

This project is proprietary software. All rights reserved.

## Support

For technical support or questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation in the `docs/` folder

## System Attributes

- **Database**: MongoDB (CMS data)
- **Storage**: Redis (workflow state), Local disk (file uploads)
- **AI Providers**: OpenAI, Anthropic
- **Framework**: Next.js 14, Payload CMS
- **Version**: 1.0.0
- **Node.js**: 18+
